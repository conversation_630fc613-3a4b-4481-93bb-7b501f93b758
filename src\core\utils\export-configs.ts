/**
 * Predefined export configurations for different data types
 */

import { ExportColumn, commonFormatters } from './export-utils';
import { formatDateEU, formatTimeEU } from './date-utils';
import { formatCurrencyEU } from './currency-utils';

// Booking-related interfaces (matching existing structure)
interface BookingExportData {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: string;
  artist?: {
    artist_name?: string;
    name?: string;
    id: string;
  };
  fee?: number;
  hourlyRate?: number;
  pricing_type?: string;
  address?: string;
  description?: string;
}

interface DocumentExportData {
  id: string;
  name: string;
  type: string;
  status: string;
  created_at: string;
  booking_title?: string;
  artist_name?: string;
  venue_name?: string;
}

interface ArtistExportData {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  genres?: string[];
  region?: string;
  pricing?: number;
  status?: string;
}

interface ClientExportData {
  id: string;
  name: string;
  type: 'company' | 'person';
  email?: string;
  phone?: string;
  address?: string;
  vat_number?: string;
  notes?: string;
  tags?: string[];
  contacts?: any[];
  primary_contact_name?: string;
  primary_contact_email?: string;
  primary_contact_phone?: string;
  primary_contact_position?: string;
  total_contacts?: number;
  created_at: string;
  updated_at: string;
}

// Complete booking export configuration
export const bookingExportColumns: ExportColumn<BookingExportData>[] = [
  {
    key: 'id',
    label: 'Booking ID',
  },
  {
    key: 'title',
    label: 'Event Title',
  },
  {
    key: 'artist',
    label: 'Artist',
    formatter: (value) => value?.artist_name || value?.name || 'Unknown',
  },
  {
    key: 'start',
    label: 'Date',
    formatter: (value) => formatDateEU(new Date(value)),
  },
  {
    key: 'start',
    label: 'Start Time',
    formatter: (value) => formatTimeEU(new Date(value)),
  },
  {
    key: 'end',
    label: 'End Time',
    formatter: (value) => formatTimeEU(new Date(value)),
  },
  {
    key: 'status',
    label: 'Status',
    formatter: commonFormatters.status,
  },
  {
    key: 'fee',
    label: 'Fee (EUR)',
    formatter: (value) => value || 0,
  },
  {
    key: 'pricing_type',
    label: 'Pricing Type',
    formatter: (value) => value || 'fixed',
  },
  {
    key: 'hourlyRate',
    label: 'Hourly Rate (EUR)',
    formatter: (value) => value || '',
  },
  {
    key: 'address',
    label: 'Address',
    formatter: (value) => value || '',
  },
  {
    key: 'description',
    label: 'Description',
    formatter: (value) => value || '',
  },
];

// Document export configuration
export const documentExportColumns: ExportColumn<DocumentExportData>[] = [
  {
    key: 'name',
    label: 'Document Name',
  },
  {
    key: 'type',
    label: 'Type',
    formatter: commonFormatters.status,
  },
  {
    key: 'status',
    label: 'Status',
    formatter: commonFormatters.status,
  },
  {
    key: 'created_at',
    label: 'Created Date',
    formatter: commonFormatters.date,
  },
  {
    key: 'booking_title',
    label: 'Related Booking',
    formatter: (value) => value || 'N/A',
  },
  {
    key: 'artist_name',
    label: 'Artist',
    formatter: (value) => value || 'N/A',
  },
  {
    key: 'venue_name',
    label: 'Venue',
    formatter: (value) => value || 'N/A',
  },
];

// Artist export configuration
export const artistExportColumns: ExportColumn<ArtistExportData>[] = [
  {
    key: 'name',
    label: 'Artist Name',
  },
  {
    key: 'email',
    label: 'Email',
    formatter: (value) => value || '',
  },
  {
    key: 'phone',
    label: 'Phone',
    formatter: (value) => value || '',
  },
  {
    key: 'genres',
    label: 'Genres',
    formatter: commonFormatters.array,
  },
  {
    key: 'region',
    label: 'Region',
    formatter: (value) => value || '',
  },
  {
    key: 'pricing',
    label: 'Base Price (EUR)',
    formatter: (value) => value || '',
  },
  {
    key: 'status',
    label: 'Status',
    formatter: commonFormatters.status,
  },
];

// Client export configuration
export const clientExportColumns: ExportColumn<ClientExportData>[] = [
  {
    key: 'id',
    label: 'Client ID',
  },
  {
    key: 'name',
    label: 'Client Name',
  },
  {
    key: 'type',
    label: 'Type',
    formatter: (value) => value === 'company' ? 'Company' : 'Individual',
  },
  {
    key: 'email',
    label: 'Email',
    formatter: (value) => value || '',
  },
  {
    key: 'phone',
    label: 'Phone',
    formatter: (value) => value || '',
  },
  {
    key: 'address',
    label: 'Address',
    formatter: (value) => value || '',
  },
  {
    key: 'vat_number',
    label: 'VAT Number',
    formatter: (value) => value || '',
  },
  {
    key: 'tags',
    label: 'Tags',
    formatter: (value) => Array.isArray(value) ? value.join(', ') : '',
  },
  {
    key: 'total_contacts',
    label: 'Total Contacts',
    formatter: (value) => value || 0,
  },
  {
    key: 'primary_contact_name',
    label: 'Primary Contact Name',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_email',
    label: 'Primary Contact Email',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_phone',
    label: 'Primary Contact Phone',
    formatter: (value) => value || '',
  },
  {
    key: 'primary_contact_position',
    label: 'Primary Contact Position',
    formatter: (value) => value || '',
  },
  {
    key: 'notes',
    label: 'Notes',
    formatter: (value) => value || '',
  },
  {
    key: 'created_at',
    label: 'Created Date',
    formatter: commonFormatters.date,
  },
  {
    key: 'updated_at',
    label: 'Last Updated',
    formatter: commonFormatters.date,
  },
];

// Combined export configurations for complex reports
export const combinedBookingDocumentColumns: ExportColumn<any>[] = [
  {
    key: 'booking_title',
    label: 'Booking Title',
  },
  {
    key: 'booking_date',
    label: 'Booking Date',
    formatter: commonFormatters.date,
  },
  {
    key: 'artist_name',
    label: 'Artist',
  },
  {
    key: 'booking_fee',
    label: 'Booking Fee (EUR)',
    formatter: (value) => value || 0,
  },
  {
    key: 'document_count',
    label: 'Documents Count',
  },
  {
    key: 'signed_documents',
    label: 'Signed Documents',
  },
  {
    key: 'pending_documents',
    label: 'Pending Documents',
  },
];

// Export type definitions for better type safety
export type BookingExportType = 'complete';
export type DocumentExportType = 'complete';
export type ArtistExportType = 'complete';
export type CombinedExportType = 'booking-documents';

// Function to get columns by export type
export const getExportColumns = (
  dataType: string,
  exportType: string = 'complete'
): ExportColumn<any>[] => {
  switch (dataType) {
    case 'bookings':
      return bookingExportColumns;
    case 'documents':
      return documentExportColumns;
    case 'artists':
      return artistExportColumns;
    case 'clients':
      return clientExportColumns;
    case 'booking-documents':
      return combinedBookingDocumentColumns;
    default:
      throw new Error(`Unknown data type: ${dataType}`);
  }
};

// Predefined export configurations
export const exportConfigurations = {
  bookings: {
    complete: {
      columns: bookingExportColumns,
      filename: 'agency-bookings',
    },
  },
  documents: {
    complete: {
      columns: documentExportColumns,
      filename: 'documents',
    },
  },
  artists: {
    complete: {
      columns: artistExportColumns,
      filename: 'artists',
    },
  },
  clients: {
    complete: {
      columns: clientExportColumns,
      filename: 'agency-clients',
    },
  },
  combined: {
    'booking-documents': {
      columns: combinedBookingDocumentColumns,
      filename: 'booking-documents-report',
    },
  },
};
