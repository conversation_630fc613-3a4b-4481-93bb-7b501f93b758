/**
 * React hook for managing export functionality
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  exportData, 
  ExportData, 
  ExportFormat, 
  ExportConfig,
  ExportColumn 
} from '../utils/export-utils';
import { getExportColumns, exportConfigurations } from '../utils/export-configs';

export interface UseExportOptions {
  onSuccess?: (count: number, format: ExportFormat) => void;
  onError?: (error: Error) => void;
  defaultFormat?: ExportFormat;
}

export interface ExportHookReturn {
  isExporting: boolean;
  exportData: <T>(data: T[], options: ExportDataOptions) => Promise<void>;
  exportWithConfig: <T>(data: T[], configKey: string, exportType?: string, format?: ExportFormat) => Promise<void>;
}

export interface ExportDataOptions {
  columns: ExportColumn<any>[];
  filename: string;
  format?: ExportFormat;
  includeTimestamp?: boolean;
}

/**
 * Hook for managing export operations
 */
export const useExport = (options: UseExportOptions = {}): ExportHookReturn => {
  const [isExporting, setIsExporting] = useState(false);
  const {
    onSuccess,
    onError,
    defaultFormat = 'csv'
  } = options;

  const handleExport = useCallback(async <T>(
    data: T[],
    exportOptions: ExportDataOptions
  ): Promise<void> => {
    setIsExporting(true);
    
    try {
      const config: ExportConfig = {
        filename: exportOptions.filename,
        format: exportOptions.format || defaultFormat,
        includeTimestamp: exportOptions.includeTimestamp ?? true,
      };

      const exportDataObj: ExportData<T> = {
        data,
        columns: exportOptions.columns,
        config,
      };

      exportData(exportDataObj);

      // Success callback
      if (onSuccess) {
        onSuccess(data.length, config.format);
      } else {
        toast.success(`Exported ${data.length} items to ${config.format.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      const exportError = error instanceof Error ? error : new Error('Export failed');
      
      if (onError) {
        onError(exportError);
      } else {
        toast.error('Failed to export data');
      }
    } finally {
      setIsExporting(false);
    }
  }, [defaultFormat, onSuccess, onError]);

  const exportWithConfig = useCallback(async <T>(
    data: T[],
    configKey: string,
    exportType: string = 'basic',
    format: ExportFormat = defaultFormat
  ): Promise<void> => {
    try {
      const columns = getExportColumns(configKey, exportType);
      const config = exportConfigurations[configKey as keyof typeof exportConfigurations];
      
      if (!config) {
        throw new Error(`No configuration found for: ${configKey}`);
      }

      const typeConfig = config[exportType as keyof typeof config];
      if (!typeConfig) {
        throw new Error(`No configuration found for type: ${exportType} in ${configKey}`);
      }

      await handleExport(data, {
        columns,
        filename: typeConfig.filename,
        format,
        includeTimestamp: true,
      });
    } catch (error) {
      console.error('Export with config error:', error);
      const exportError = error instanceof Error ? error : new Error('Export configuration failed');
      
      if (onError) {
        onError(exportError);
      } else {
        toast.error('Failed to export data: ' + exportError.message);
      }
    }
  }, [handleExport, defaultFormat, onError]);

  return {
    isExporting,
    exportData: handleExport,
    exportWithConfig,
  };
};

/**
 * Hook specifically for booking exports with predefined configurations
 */
export const useBookingExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportBookings = useCallback(async (
    bookings: any[],
    type: 'basic' | 'detailed' = 'basic',
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(bookings, 'bookings', type, format);
  }, [exportHook]);

  return {
    ...exportHook,
    exportBookings,
  };
};

/**
 * Hook specifically for document exports
 */
export const useDocumentExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportDocuments = useCallback(async (
    documents: any[],
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(documents, 'documents', 'basic', format);
  }, [exportHook]);

  return {
    ...exportHook,
    exportDocuments,
  };
};

/**
 * Hook for combined/complex exports
 */
export const useCombinedExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportBookingDocuments = useCallback(async (
    combinedData: any[],
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(combinedData, 'booking-documents', 'booking-documents', format);
  }, [exportHook]);

  return {
    ...exportHook,
    exportBookingDocuments,
  };
};
