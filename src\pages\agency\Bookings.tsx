import { useState, useEffect } from 'react';
import { isAfter, isBefore, isEqual } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import BookingCalendar from '@/components/bookings/BookingCalendar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon, Search, Plus, Eye, RotateCcw, Loader2, MoreVertical, Trash2, User, ChevronLeft, ChevronRight, Clock, Download, ChevronDown } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { supabase } from '@/core/api/supabase';
import { toast } from "sonner";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { BookingModal } from '@/features/bookings/components';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { formatDateEU, formatTimeEU, formatCurrencyEU, calculateTotalPrice, useIsMobile } from '@/core';

interface ArtistInfo {
  artist_name?: string;
  name?: string;
  id: string;
}

interface BookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  artist: ArtistInfo;
  fee?: number;
  hourlyRate?: number | null;
  pricing_type?: string;
}

interface CalendarBookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  artist: string;
  address?: string;
}

const AgencyBookings = () => {
  const [bookings, setBookings] = useState<BookingEvent[]>([]);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [timeFilter, setTimeFilter] = useState('future');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [isDeleting, setIsDeleting] = useState(false);
  const [bookingToDelete, setBookingToDelete] = useState<string | null>(null);
  const [showDeleteBookingDialog, setShowDeleteBookingDialog] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isExporting, setIsExporting] = useState(false);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'default';
      default:
        return 'outline';
    }
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    setIsLoading(true);
    try {
      // First ensure we have a valid session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('Error getting session:', sessionError);
        toast.error("Authentication error. Please try logging in again.");
        setIsLoading(false);
        return;
      }

      if (!sessionData.session) {
        console.error('No active session found');
        toast.error("Your session has expired. Please log in again.");
        setIsLoading(false);
        return;
      }

      // Now get the user with the valid session
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('Error fetching auth user:', userError);
        toast.error("Could not retrieve user information.");
        setIsLoading(false);
        return;
      }

      const user = userData?.user;
      if (!user) {
        console.error('User not authenticated');
        toast.error("You must be logged in to view bookings.");
        setIsLoading(false);
        return;
      }

      console.log('Fetching agencies for user:', user.id);

      // Get user's agency entities from user_with_entities view
      const { data: agencyEntities, error: entityError } = await supabase
        .from('user_with_entities')
        .select('entity_id')
        .eq('user_id', user.id)
        .eq('entity_type', 'agency');

      if (entityError) {
        console.error('Error fetching user agencies:', entityError);
        toast.error("Could not find your agencies. Using your user ID as agency ID.");

        const agencyId = user.id;

        const { data, error } = await supabase
          .from('bookings')
          .select(`
            id,
            title,
            booking_start,
            booking_end,
            status,
            price,
            pricing_type,
            artist_id
          `)
          .eq('owner_entity_id', agencyId);

        if (error) {
          toast.error("Error loading bookings");
          throw error;
        }

        const bookingsWithArtistDetails = await Promise.all(data.map(async (booking) => {
          // Get artist entity name
          const { data: entityData } = await supabase
            .from('entities')
            .select('name')
            .eq('id', booking.artist_id)
            .single();

          // Calculate total price for hourly bookings
          let fee = parseFloat(String(booking.price || "0"));
          if (booking.pricing_type === 'hourly') {
            fee = calculateTotalPrice(
              booking.price,
              'hourly',
              booking.booking_start,
              booking.booking_end
            );
          }

          return {
            id: booking.id,
            title: booking.title,
            start: new Date(booking.booking_start),
            end: new Date(booking.booking_end),
            status: booking.status as "confirmed" | "pending" | "cancelled" | "completed",
            artist: {
              artist_name: entityData?.name || 'Unknown Artist',
              name: entityData?.name || 'Unknown Artist',
              id: booking.artist_id
            },
            fee: fee,
            hourlyRate: booking.pricing_type === 'hourly' ? parseFloat(String(booking.price || "0")) : null,
            pricing_type: booking.pricing_type
          };
        }));

        setBookings(bookingsWithArtistDetails);
        return;
      }

      if (agencyEntities && agencyEntities.length > 0) {
        // Get all entity IDs for the user's agencies
        const agencyEntityIds = agencyEntities.map(entity => entity.entity_id);
        console.log('Agency entity IDs:', agencyEntityIds);

        // Try to fetch bookings using owner_entity_id (new schema)
        let bookingsData: any[] = [];
        let error = null;

        try {
          // First try with owner_entity_id
          const { data: ownerData, error: ownerError } = await supabase
            .from('bookings')
            .select(`
              id,
              title,
              booking_start,
              booking_end,
              status,
              price,
              pricing_type,
              artist_id
            `)
            .in('owner_entity_id', agencyEntityIds);

          if (ownerError) {
            console.error('Error fetching bookings with owner_entity_id:', ownerError);
            error = ownerError;
          } else if (ownerData && ownerData.length > 0) {
            console.log('Found bookings with owner_entity_id:', ownerData.length);
            bookingsData = ownerData;
          } else {
            // If no bookings found with owner_entity_id, try venue_id (old schema)
            console.log('No bookings found with owner_entity_id, trying venue_id');
            const { data: venueData, error: venueError } = await supabase
              .from('bookings')
              .select(`
                id,
                title,
                booking_start,
                booking_end,
                status,
                price,
                pricing_type,
                artist_id
              `)
              .in('venue_id', agencyEntityIds);

            if (venueError) {
              console.error('Error fetching bookings with venue_id:', venueError);
              error = venueError;
            } else if (venueData) {
              console.log('Found bookings with venue_id:', venueData.length);
              bookingsData = venueData;
            }
          }
        } catch (err) {
          console.error('Error in booking fetch operations:', err);
          error = err;
        }

        if (error) {
          toast.error("Error loading bookings");
          throw error;
        }

        console.log('Fetched bookings:', bookingsData);

        const bookingsWithArtistDetails = await Promise.all(bookingsData.map(async (booking: any) => {
          // Get artist entity name
          const { data: entityData } = await supabase
            .from('entities')
            .select('name')
            .eq('id', booking.artist_id)
            .single();

          // Calculate total price for hourly bookings
          let fee = parseFloat(String(booking.price || "0"));
          if (booking.pricing_type === 'hourly') {
            fee = calculateTotalPrice(
              booking.price,
              'hourly',
              booking.booking_start,
              booking.booking_end
            );
          }

          return {
            id: booking.id,
            title: booking.title,
            start: new Date(booking.booking_start),
            end: new Date(booking.booking_end),
            status: booking.status as "confirmed" | "pending" | "cancelled" | "completed",
            artist: {
              artist_name: entityData?.name || 'Unknown Artist',
              name: entityData?.name || 'Unknown Artist',
              id: booking.artist_id
            },
            fee: fee,
            hourlyRate: booking.pricing_type === 'hourly' ? parseFloat(String(booking.price || "0")) : null,
            pricing_type: booking.pricing_type
          };
        }));

        setBookings(bookingsWithArtistDetails);
      } else {
        toast.warning("No agencies found for this user");
        setBookings([]);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Failed to load bookings.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sort bookings based on time filter
  const sortedBookings = [...bookings].sort((a, b) => {
    if (timeFilter === 'past') {
      // For past bookings: most recent first
      return new Date(b.start).getTime() - new Date(a.start).getTime();
    } else {
      // For future bookings: earliest first
      return new Date(a.start).getTime() - new Date(b.start).getTime();
    }
  });

  const filteredEvents = sortedBookings.filter(event => {
    if (timeFilter === 'future' && isBefore(event.start, new Date())) return false;
    if (timeFilter === 'past' && (isAfter(event.start, new Date()) || isEqual(event.start, new Date()))) return false;
    if (dateRange?.from && isBefore(event.start, dateRange.from)) return false;
    if (dateRange?.to && isAfter(event.start, dateRange.to)) return false;

    const minPriceNum = minPrice ? parseFloat(minPrice) : null;
    const maxPriceNum = maxPrice ? parseFloat(maxPrice) : null;

    if (minPriceNum !== null && event.fee && event.fee < minPriceNum) return false;
    if (maxPriceNum !== null && event.fee && event.fee > maxPriceNum) return false;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return event.title.toLowerCase().includes(query) ||
             (event.artist?.artist_name && event.artist.artist_name.toLowerCase().includes(query)) ||
             (event.artist?.name && event.artist.name.toLowerCase().includes(query));
    }
    return true;
  });

  const indexOfLastEvent = currentPage * rowsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - rowsPerPage;
  const currentEvents = filteredEvents.slice(indexOfFirstEvent, indexOfLastEvent);
  const pageCount = Math.ceil(filteredEvents.length / rowsPerPage);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= pageCount) {
      setCurrentPage(page);
    }
  };

  const resetFilters = () => {
    setDateRange({
      from: undefined,
      to: undefined
    });
    setSearchQuery('');
    setTimeFilter('future');
    setMinPrice('');
    setMaxPrice('');
    setCurrentPage(1);
  };

  // CSV Export functionality
  const convertToCSV = (data: BookingEvent[]) => {
    const headers = [
      'Event Title',
      'Artist',
      'Date',
      'Start Time',
      'End Time',
      'Status',
      'Fee (EUR)',
      'Pricing Type',
      'Hourly Rate (EUR)'
    ];

    const csvContent = [
      headers.join(','),
      ...data.map(booking => [
        `"${booking.title.replace(/"/g, '""')}"`,
        `"${(booking.artist?.artist_name || booking.artist?.name || 'Unknown').replace(/"/g, '""')}"`,
        `"${formatDateEU(booking.start)}"`,
        `"${formatTimeEU(booking.start)}"`,
        `"${formatTimeEU(booking.end)}"`,
        `"${booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}"`,
        `"${booking.fee || 0}"`,
        `"${booking.pricing_type || 'fixed'}"`,
        `"${booking.hourlyRate || ''}"`,
      ].join(','))
    ].join('\n');

    return csvContent;
  };

  const downloadCSV = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportCSV = async () => {
    setIsExporting(true);
    try {
      // Use the same filtered data that's currently displayed
      const csvContent = convertToCSV(filteredEvents);
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `agency-bookings-${timestamp}.csv`;
      downloadCSV(csvContent, filename);
      toast.success(`Exported ${filteredEvents.length} bookings to CSV`);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      toast.error('Failed to export bookings');
    } finally {
      setIsExporting(false);
    }
  };

  // Need to fetch the location data for each booking
  const fetchBookingLocations = async () => {
    const bookingIds = bookings.map(booking => booking.id);
    if (bookingIds.length === 0) return {};

    try {
      const { data, error } = await supabase
        .from('bookings')
        .select('id, location')
        .in('id', bookingIds);

      if (error) {
        console.error('Error fetching booking locations:', error);
        return {};
      }

      // Create a map of booking ID to location
      const locationMap: Record<string, string> = {};
      data.forEach(booking => {
        locationMap[booking.id] = booking.location || '';
      });

      return locationMap;
    } catch (err) {
      console.error('Exception in fetchBookingLocations:', err);
      return {};
    }
  };

  // Use useEffect to fetch locations when bookings change
  const [bookingLocations, setBookingLocations] = useState<Record<string, string>>({});

  useEffect(() => {
    if (bookings.length > 0) {
      fetchBookingLocations().then(locations => {
        setBookingLocations(locations);
      });
    }
  }, [bookings]);

  const calendarEvents: CalendarBookingEvent[] = bookings.map(booking => {
    // Normalize status to ensure consistency (handle both 'canceled' and 'cancelled' spellings)
    let normalizedStatus = booking.status;
    if (normalizedStatus === 'canceled') {
      normalizedStatus = 'cancelled';
    }

    return {
      id: booking.id,
      title: booking.title,
      start: booking.start,
      end: booking.end,
      status: normalizedStatus,
      artist: booking.artist?.artist_name || booking.artist?.name || 'Unknown Artist',
      address: bookingLocations[booking.id] || 'No address provided' // Use the actual booking location
    };
  });

  const navigateToBookingDetails = (id: string) => {
    navigate(`/agency/bookings/${id}`);
  };

  const handleOpenBookingModal = () => {
    setSelectedDate(new Date());
    setShowBookingModal(true);
  };

  const handleCloseBookingModal = () => {
    setShowBookingModal(false);
    fetchBookings();
  };

  const handleRowsPerPageChange = (value: string) => {
    setRowsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  const handleDeleteBookingClick = (bookingId: string) => {
    setBookingToDelete(bookingId);
    setShowDeleteBookingDialog(true);
  };

  const handleDeleteBooking = async () => {
    if (!bookingToDelete) return;

    try {
      setIsDeleting(true);

      const bookingToDeleteInfo = bookings.find(b => b.id === bookingToDelete);

      const { data: documents, error: docError } = await supabase
        .from("documents")
        .select("id, file_url")
        .eq("booking_id", bookingToDelete);

      if (docError) {
        console.error("Error checking for documents:", docError);
        throw new Error("Failed to check for associated documents");
      }

      if (documents && documents.length > 0) {
        console.log(`Deleting ${documents.length} documents associated with booking ${bookingToDelete}`);

        for (const doc of documents) {
          const { error: deleteDocError } = await supabase
            .from("documents")
            .delete()
            .eq("id", doc.id);

          if (deleteDocError) {
            console.error(`Error deleting document ${doc.id}:`, deleteDocError);
          }

          if (doc.file_url) {
            try {
              const filePath = doc.file_url.split('/').pop();
              if (filePath) {
                await supabase.storage
                  .from('documents')
                  .remove([`${bookingToDelete}/${filePath}`]);
              }
            } catch (storageError) {
              console.error("Error deleting file from storage:", storageError);
            }
          }
        }
      }

      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", bookingToDelete);

      if (error) {
        console.error("Error deleting booking:", error);
        throw error;
      }

      await fetchBookings();

      if (bookingToDeleteInfo) {
        toast.success("Booking deleted", {
          description: `${bookingToDeleteInfo.title} has been deleted`,
          action: {
            label: "Undo",
            onClick: async () => {
              try {
                // Get the user's agency entity ID
                const { data: { user } } = await supabase.auth.getUser();
                const { data: agencyEntities } = await supabase
                  .from('user_with_entities')
                  .select('entity_id')
                  .eq('user_id', user?.id || '')
                  .eq('entity_type', 'agency')
                  .limit(1);

                const agencyEntityId = agencyEntities && agencyEntities.length > 0
                  ? agencyEntities[0].entity_id
                  : user?.id || '';

                // Try to restore with venue_id (most compatible approach)
                try {
                  const { error: restoreError } = await supabase
                    .from("bookings")
                    .insert({
                      title: bookingToDeleteInfo.title,
                      booking_start: bookingToDeleteInfo.start.toISOString(),
                      booking_end: bookingToDeleteInfo.end.toISOString(),
                      status: bookingToDeleteInfo.status,
                      price: bookingToDeleteInfo.fee || 0,
                      artist_id: bookingToDeleteInfo.artist?.id || '',
                      venue_id: agencyEntityId,
                      pricing_type: bookingToDeleteInfo.pricing_type || 'fixed',
                      created_by: user?.id || ''
                    });

                  if (restoreError) {
                    console.error('Failed to restore booking:', restoreError);
                    throw restoreError;
                  }
                } catch (error) {
                  console.error('Failed to restore booking:', error);
                  throw error;
                }

                await fetchBookings();
                toast.success("Booking restored successfully");
              } catch (error) {
                console.error("Error restoring booking:", error);
                toast.error("Failed to restore booking");
              }
            }
          }
        });
      } else {
        toast.success("Booking deleted successfully");
      }
    } catch (error: any) {
      console.error("Error deleting booking:", error);
      toast.error("Failed to delete booking: " + (error.message || "Unknown error"));
    } finally {
      setIsDeleting(false);
      setShowDeleteBookingDialog(false);
      setBookingToDelete(null);
    }
  };

  return <DashboardLayout userType="agency">
      <DeleteConfirmationDialog
        open={showDeleteBookingDialog}
        onOpenChange={setShowDeleteBookingDialog}
        onConfirm={handleDeleteBooking}
        title="Delete Booking"
        description={`Are you sure you want to delete this booking? This will also delete all associated documents and cannot be undone.`}
        itemType="booking"
        isDeleting={isDeleting}
      />

      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Bookings</h1>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <Tabs defaultValue="list" className="w-full">
            <div className="flex justify-between items-center">
              <TabsList className="bg-zinc-200">
                <TabsTrigger value="list">List View</TabsTrigger>
                <TabsTrigger value="calendar">Calendar View</TabsTrigger>
              </TabsList>

              <div className="flex gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" disabled={isExporting}>
                      {isExporting ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Download className="h-4 w-4 mr-2" />
                      )}
                      Export
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleExportCSV} disabled={isExporting}>
                      <Download className="mr-2 h-4 w-4" />
                      Export as CSV
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-zinc-950 hover:bg-zinc-800"
                  onClick={handleOpenBookingModal}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Booking
                </Button>
              </div>
            </div>

            <TabsContent value="list" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>All Bookings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-3">
                    {isMobile ? (
                      <div className="space-y-3">
                        {/* Search bar - full width on mobile */}
                        <div className="relative w-full">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search artists or event titles..."
                            value={searchQuery}
                            onChange={e => setSearchQuery(e.target.value)}
                            className="pl-8 w-full"
                          />
                        </div>

                        {/* Filter controls in a grid */}
                        <div className="grid grid-cols-2 gap-2">
                          <Select value={timeFilter} onValueChange={setTimeFilter}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Time period" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All bookings</SelectItem>
                              <SelectItem value="future">Future bookings</SelectItem>
                              <SelectItem value="past">Past bookings</SelectItem>
                            </SelectContent>
                          </Select>

                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-start text-left font-normal text-xs h-10 truncate">
                                <CalendarIcon className="mr-1 h-3.5 w-3.5 flex-shrink-0" />
                                <span className="truncate">
                                  {dateRange?.from ? dateRange.to ?
                                    `${formatDateEU(dateRange.from)} - ${formatDateEU(dateRange.to)}` :
                                    formatDateEU(dateRange.from) : "Date range"}
                                </span>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                initialFocus
                                mode="range"
                                defaultMonth={dateRange?.from}
                                selected={dateRange}
                                onSelect={setDateRange}
                                numberOfMonths={1}
                              />
                            </PopoverContent>
                          </Popover>

                          {/* Price range inputs in a single row */}
                          <div className="col-span-2 grid grid-cols-2 gap-2">
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="Min €"
                                value={minPrice}
                                onChange={e => setMinPrice(e.target.value)}
                                className="w-full pl-6"
                              />
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">€</span>
                            </div>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="Max €"
                                value={maxPrice}
                                onChange={e => setMaxPrice(e.target.value)}
                                className="w-full pl-6"
                              />
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">€</span>
                            </div>
                          </div>

                          {/* Reset button - full width */}
                          <Button
                            variant="outline"
                            onClick={resetFilters}
                            className="col-span-2 h-9 text-xs"
                            title="Reset Filters"
                          >
                            <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                            Reset Filters
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-3 items-center">
                        <div className="relative flex-1 min-w-[200px]">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input placeholder="Search artists or event titles..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="pl-8" />
                        </div>

                        <Select value={timeFilter} onValueChange={setTimeFilter}>
                          <SelectTrigger className="w-[140px]">
                            <SelectValue placeholder="Time period" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All bookings</SelectItem>
                            <SelectItem value="future">Future bookings</SelectItem>
                            <SelectItem value="past">Past bookings</SelectItem>
                          </SelectContent>
                        </Select>

                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="outline" className="w-auto justify-start text-left font-normal">
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {dateRange?.from ? dateRange.to ? <>
                                    {formatDateEU(dateRange.from)} - {formatDateEU(dateRange.to)}
                                  </> : formatDateEU(dateRange.from) : "Date range"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar initialFocus mode="range" defaultMonth={dateRange?.from} selected={dateRange} onSelect={setDateRange} numberOfMonths={2} />
                          </PopoverContent>
                        </Popover>

                        <div className="flex items-center gap-2">
                          <Input type="number" placeholder="Min €" value={minPrice} onChange={e => setMinPrice(e.target.value)} className="w-24" />
                          <span>-</span>
                          <Input type="number" placeholder="Max €" value={maxPrice} onChange={e => setMaxPrice(e.target.value)} className="w-24" />
                        </div>

                        <Button variant="outline" onClick={resetFilters} size="icon" title="Reset Filters">
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  <div>
                    {isLoading ? (
                      <div className="flex justify-center items-center py-20">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                        <span className="ml-2 text-lg text-gray-500">Loading bookings...</span>
                      </div>
                    ) : (
                      isMobile ? (
                        <div className="flex flex-col space-y-3 p-0">
                          {currentEvents.length > 0 ? (
                            currentEvents.map(event => (
                              <div
                                key={event.id}
                                className="border rounded-lg p-3 cursor-pointer hover:bg-muted/50 transition-colors shadow-sm"
                                onClick={() => navigateToBookingDetails(event.id)}
                              >
                                {/* Date badge with calendar icon and status badge */}
                                <div className="flex items-center mb-2">
                                  <div className="bg-gray-100 rounded-md p-1.5 mr-2 flex items-center">
                                    <span className="ml-1 text-xs font-medium">{formatDateEU(event.start)}</span>
                                  </div>
                                  <div className="flex items-center ml-auto">
                                    <Badge
                                      variant={getStatusBadgeVariant(event.status)}
                                      className="mr-2 flex-shrink-0"
                                    >
                                      {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                                    </Badge>
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                          <span className="sr-only">Open menu</span>
                                          <MoreVertical className="h-3.5 w-3.5" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          navigateToBookingDetails(event.id);
                                        }}>
                                          <Eye className="mr-2 h-4 w-4" />
                                          <span>View Booking</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteBookingClick(event.id);
                                          }}
                                          className="text-red-600"
                                          disabled={isDeleting}
                                        >
                                          <Trash2 className="mr-2 h-4 w-4" />
                                          <span>Delete</span>
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                </div>

                                {/* Event title */}
                                <h4 className="font-medium text-sm mb-2 line-clamp-1">{event.title}</h4>

                                {/* Event details in a clean layout */}
                                <div className="space-y-1.5 text-xs">
                                  {/* Time info */}
                                  <div className="flex items-center text-gray-600">
                                    <Clock className="h-3 w-3 mr-1.5 text-gray-500" />
                                    <span>{formatTimeEU(event.start)} - {formatTimeEU(event.end)}</span>
                                  </div>

                                  {/* Artist info */}
                                  <div className="flex items-center text-gray-600">
                                    <User className="h-3 w-3 mr-1.5 text-gray-500" />
                                    <span className="truncate">{event.artist?.artist_name || event.artist?.name || 'Unknown'}</span>
                                  </div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8 bg-gray-50 rounded-lg">
                              <p className="text-gray-500">No bookings found matching your filters</p>
                              {(searchQuery || timeFilter !== 'all' || dateRange?.from || minPrice || maxPrice) && (
                                <Button variant="outline" onClick={resetFilters} className="mt-4">
                                  <RotateCcw className="mr-2 h-4 w-4" />
                                  Reset Filters
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Event</TableHead>
                              <TableHead>Artist</TableHead>
                              <TableHead>Date & Time</TableHead>
                              <TableHead>Fee</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {currentEvents.length > 0 ? (
                              currentEvents.map(event => (
                                <TableRow key={event.id} className="cursor-pointer" onClick={() => navigateToBookingDetails(event.id)}>
                                  <TableCell className="font-medium">{event.title}</TableCell>
                                  <TableCell>{event.artist?.artist_name || event.artist?.name || 'Unknown'}</TableCell>
                                  <TableCell>
                                    {formatDateEU(event.start)} • {formatTimeEU(event.start)}
                                  </TableCell>
                                  <TableCell>
                                    {event.pricing_type === 'hourly' ? (
                                      <div>
                                        <div>{formatCurrencyEU(event.fee || 0)}</div>
                                        <div className="text-xs text-gray-500">
                                          ({formatCurrencyEU(event.hourlyRate || 0)}/hour)
                                        </div>
                                      </div>
                                    ) : (
                                      formatCurrencyEU(event.fee || 0)
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                      ${event.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                        event.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                        event.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                                        'bg-red-100 text-red-800'}`}>
                                      {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                                    </span>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                          <span className="sr-only">Open menu</span>
                                          <MoreVertical className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          navigateToBookingDetails(event.id);
                                        }}>
                                          <Eye className="mr-2 h-4 w-4" />
                                          <span>View Booking</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteBookingClick(event.id);
                                          }}
                                          className="text-red-600"
                                          disabled={isDeleting}
                                        >
                                          <Trash2 className="mr-2 h-4 w-4" />
                                          <span>Delete</span>
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </TableCell>
                                </TableRow>
                              ))
                            ) : (
                              <TableRow>
                                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                  No bookings found matching your filters
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                        </div>
                      )
                    )}
                  </div>

                  <div className="mt-6 flex flex-col sm:flex-row gap-4 items-center justify-between">
                    {isMobile ? (
                      <div className="w-full flex justify-between items-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="px-2"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>

                        <span className="text-sm">
                          Page {currentPage} of {pageCount || 1}
                        </span>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === pageCount || pageCount === 0}
                          className="px-2"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Rows per page:</span>
                          <Select
                            value={String(rowsPerPage)}
                            onValueChange={handleRowsPerPageChange}
                          >
                            <SelectTrigger className="h-8 w-[70px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="5">5</SelectItem>
                              <SelectItem value="10">10</SelectItem>
                              <SelectItem value="20">20</SelectItem>
                              <SelectItem value="50">50</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {filteredEvents.length > rowsPerPage && (
                          <Pagination>
                            <PaginationContent>
                              <PaginationItem>
                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                              </PaginationItem>

                              {Array.from({ length: Math.min(pageCount, 5) }, (_, i) => {
                                let pageNum: number;
                                if (pageCount <= 5) {
                                  pageNum = i + 1;
                                } else if (currentPage <= 3) {
                                  pageNum = i + 1;
                                } else if (currentPage >= pageCount - 2) {
                                  pageNum = pageCount - 4 + i;
                                } else {
                                  pageNum = currentPage - 2 + i;
                                }
                                return (
                                  <PaginationItem key={i}>
                                    <PaginationLink onClick={() => handlePageChange(pageNum)} isActive={pageNum === currentPage}>
                                      {pageNum}
                                    </PaginationLink>
                                  </PaginationItem>
                                );
                              })}

                              <PaginationItem>
                                <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className={currentPage === pageCount ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        )}
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calendar" className="mt-6">
              <BookingCalendar events={calendarEvents} userType="agency" />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <BookingModal
        open={showBookingModal}
        onClose={handleCloseBookingModal}
        selectedDate={selectedDate}
        userType="agency"
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteBookingDialog}
        onOpenChange={setShowDeleteBookingDialog}
        onConfirm={handleDeleteBooking}
        title="Delete Booking"
        description={`Are you sure you want to delete this booking? This will also delete all associated documents and cannot be undone.`}
        itemType="booking"
        isDeleting={isDeleting}
      />
    </DashboardLayout>;
};

export default AgencyBookings;
